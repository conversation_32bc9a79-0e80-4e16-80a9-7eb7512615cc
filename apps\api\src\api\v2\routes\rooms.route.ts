import { handleEffectError } from '@/api/v2/utils/error-handler';
import { RoomsRuntime } from '@/infrastructure/runtimes/rooms.runtime';
import { effectValidator } from '@hono/effect-validator';
import {
  CollectionViewParamSchema,
  ResourceIdSchema,
  ResourceQuerySchema,
  RoomInputSchema,
  RoomListSchema,
  RoomSelectSchema,
} from '@rie/domain/schemas';
import { dbRoomsToRooms } from '@rie/domain/serializers';
import type { HonoVariables } from '@rie/domain/types';
import { RoomsServiceLive } from '@rie/services';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver } from 'hono-openapi/effect';

// OpenAPI route descriptions
export const getAllRoomsRoute = describeRoute({
  description: 'Lister toutes les salles',
  operationId: 'getAllRooms',
  parameters: [
    {
      name: 'view',
      in: 'query',
      required: false,
      schema: resolver(Schema.Union(Schema.Literal('list'), Schema.Literal('select'))),
      description: 'Type de vue (list, select)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(Schema.Array(Schema.Union(RoomListSchema, RoomSelectSchema))),
        },
      },
      description: 'Salles retournées',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Rooms'],
});

export const getRoomByIdRoute = describeRoute({
  description: 'Obtenir une salle par ID',
  operationId: 'getRoomById',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID de la salle (format CUID)',
    },
    {
      name: 'view',
      in: 'query',
      required: false,
      schema: resolver(Schema.Union(Schema.Literal('list'), Schema.Literal('edit'))),
      description: 'Type de vue (list, edit)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(RoomListSchema),
        },
      },
      description: 'Salle trouvée',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Salle non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Rooms'],
});

export const createRoomRoute = describeRoute({
  description: 'Créer une salle',
  operationId: 'createRoom',
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(RoomInputSchema),
        example: {
          number: 'NEW-ROOM-001',
          area: 25.5,
          floorLoad: null,
          buildingId: 'bw6c4rifj8239sc6gheruphi',
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(RoomListSchema),
        },
      },
      description: 'Salle créée',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description:
        "Erreur de validation - Données d'entrée invalides ou champs requis manquants",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Clé étrangère non trouvée - Le bâtiment n'existe pas",
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Rooms'],
});

export const updateRoomRoute = describeRoute({
  description: 'Mettre à jour une salle',
  operationId: 'updateRoom',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID de la salle (format CUID)',
      example: 'vot1nira0alg10houmycxefw',
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(RoomInputSchema),
        example: {
          number: 'UPDATED-ROOM-001',
          area: 30.0,
          floorLoad: null,
          buildingId: 'bw6c4rifj8239sc6gheruphi',
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(RoomListSchema),
        },
      },
      description: 'Salle mise à jour',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Erreur de validation - Données d'entrée invalides",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Salle non trouvée ou le bâtiment n'existe pas",
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Rooms'],
});

export const deleteRoomRoute = describeRoute({
  description: 'Supprimer une salle',
  operationId: 'deleteRoom',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID de la salle (format CUID)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({ success: Schema.Boolean, message: Schema.String }),
          ),
        },
      },
      description: 'Salle supprimée',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Salle non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Rooms'],
});

const roomsRoute = new Hono<{
  Variables: HonoVariables;
}>();

roomsRoute.get(
  '/',
  getAllRoomsRoute,
  effectValidator('query', CollectionViewParamSchema),
  async (ctx) => {
    const { view } = ctx.req.valid('query');

    const program = Effect.gen(function* () {
      const roomService = yield* RoomsServiceLive;
      const rooms = yield* roomService.getAllRooms();
      return dbRoomsToRooms(rooms, view);
    });
    const result = await RoomsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

roomsRoute.get(
  '/:id',
  getRoomByIdRoute,
  effectValidator('param', Schema.Struct({ id: ResourceIdSchema })),
  effectValidator('query', ResourceQuerySchema),
  async (ctx) => {
    const id = ctx.req.param('id');
    const { view } = ctx.req.valid('query');
    const program = Effect.gen(function* () {
      const roomService = yield* RoomsServiceLive;
      const room = yield* roomService.getRoomById(id);
      // Return raw room data for now - serializer needs repository updates to work properly
      return room;
    });
    const result = await RoomsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

roomsRoute.post(
  '/',
  createRoomRoute,
  effectValidator('json', RoomInputSchema),
  async (ctx) => {
    const body = ctx.req.valid('json');
    const user = ctx.get('user');
    const program = Effect.gen(function* () {
      const roomService = yield* RoomsServiceLive;
      return yield* roomService.createRoom({
        ...body,
        modifiedBy: user?.id,
      });
    });
    const result = await RoomsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value, 201);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

roomsRoute.put(
  '/:id',
  updateRoomRoute,
  effectValidator('param', Schema.Struct({ id: ResourceIdSchema })),
  effectValidator('json', RoomInputSchema),
  async (ctx) => {
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json');
    const user = ctx.get('user');
    const program = Effect.gen(function* () {
      const roomService = yield* RoomsServiceLive;
      return yield* roomService.updateRoom({
        id,
        room: {
          ...body,
          modifiedBy: user?.id,
        },
      });
    });
    const result = await RoomsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

roomsRoute.delete(
  '/:id',
  deleteRoomRoute,
  effectValidator('param', Schema.Struct({ id: ResourceIdSchema })),
  async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
      const roomService = yield* RoomsServiceLive;
      return yield* roomService.deleteRoom(id);
    });
    const result = await RoomsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json({ success: true, message: 'Room deleted' });
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

export { roomsRoute };

