import {
  DbRoomInputSchema,
  DbRoomSelectSchema,
} from '@rie/db-schema/entity-schemas';
import * as Schema from 'effect/Schema';

// — Full Room shape (rooms don't have translations in the DB schema)
export const RoomSchema = Schema.Struct({
  ...DbRoomSelectSchema.omit('modifiedBy').fields,
  modifiedBy: Schema.NullishOr(Schema.String),
});

// — Database schema (for serializers)
export const DbRoomSchema = RoomSchema;

// — Room List view schema (for directory table)
export const RoomListSchema = Schema.Struct({
  id: Schema.String,
  numero: Schema.String, // numéro de la salle
  building: Schema.NullishOr(Schema.String), // nom du bâtiment
  jurisdiction: Schema.NullishOr(Schema.String), // juridiction
  lastUpdatedAt: Schema.String,
  // Champs internes pour les actions
  area: Schema.NullishOr(Schema.Number),
  floorLoad: Schema.NullishOr(Schema.Number),
  buildingId: Schema.NullishOr(Schema.String),
});

// — Room Select view schema
export const RoomSelectSchema = Schema.Struct({
  value: Schema.String,
  label: Schema.String,
});

// — Room Edit view schema (form-compatible format)
export const RoomEditSchema = Schema.Struct({
  id: Schema.optional(Schema.String), // Optional for create, required for update
  number: Schema.String,
  area: Schema.NullishOr(Schema.Number),
  floorLoad: Schema.NullishOr(Schema.Number),
  buildingId: Schema.NullishOr(Schema.String),
  isActive: Schema.NullishOr(Schema.Boolean),
  categories: Schema.Array(
    Schema.Struct({
      value: Schema.String,
      label: Schema.String,
    }),
  ),
});

// — Room Detail view schema (same as list for now)
export const RoomDetailSchema = RoomListSchema;

// — Input (create/update) shape
export const RoomInputSchema = Schema.Struct({
  ...DbRoomInputSchema.omit('id').fields,
});
