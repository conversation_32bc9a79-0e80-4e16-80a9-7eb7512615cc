import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';
import {
  RoomEditSchema,
  RoomInputSchema,
  RoomListSchema,
  RoomSelectSchema
} from '../schemas';
import type {
  CollectionViewType,
  ResourceViewType,
  RoomList,
  RoomSelect
} from '../types';

// Schema for database room with building relation and categories (as returned by repository)
export const DbRoomWithRelationsSchema = Schema.Struct({
  id: Schema.String,
  number: Schema.String,
  area: Schema.NullishOr(Schema.Number),
  floorLoad: Schema.NullishOr(Schema.Number),
  buildingId: Schema.NullishOr(Schema.String),
  createdAt: Schema.String,
  updatedAt: Schema.String,
  modifiedBy: Schema.NullishOr(Schema.String),
  isActive: Schema.NullishOr(Schema.Boolean),
  building: Schema.NullishOr(
    Schema.Struct({
      id: Schema.String,
      translations: Schema.optional(Schema.Array(
        Schema.Struct({
          id: Schema.String,
          locale: Schema.String,
          name: Schema.NullishOr(Schema.String),
        }),
      )),
    }),
  ),
  categories: Schema.optional(Schema.Array(
    Schema.Struct({
      category: Schema.Struct({
        id: Schema.String,
        translations: Schema.optional(Schema.Array(
          Schema.Struct({
            id: Schema.String,
            locale: Schema.String,
            name: Schema.NullishOr(Schema.String),
            description: Schema.NullishOr(Schema.String),
          }),
        )),
      }),
    }),
  )),
});

// Backward compatibility schema for rooms without categories
export const DbRoomWithBuildingSchema = DbRoomWithRelationsSchema.omit('categories');

// Schema transformer for converting database room to list view
export const DbRoomToRoomList = Schema.transformOrFail(
  DbRoomWithBuildingSchema,
  RoomListSchema,
  {
    strict: true,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          // Get building name from building relation
          let buildingName = null;
          if (raw.building?.translations) {
            const buildingTranslations = raw.building.translations;
            buildingName =
              buildingTranslations.find((t) => t.locale === 'fr')?.name ||
              buildingTranslations.find((t) => t.locale === 'en')?.name ||
              buildingTranslations?.[0]?.name ||
              null;
          }

          // TODO: Get jurisdiction from institution/unit hierarchy
          const jurisdiction = null;

          return {
            id: String(raw.id || ''),
            numero: String(raw.number || ''),
            building: buildingName,
            jurisdiction,
            lastUpdatedAt: String(raw.updatedAt || ''),
            area: typeof raw.area === 'number' ? raw.area : null,
            floorLoad: typeof raw.floorLoad === 'number' ? raw.floorLoad : null,
            buildingId: raw.buildingId ? String(raw.buildingId) : null,
            isActive: raw.isActive ?? true,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse room for list view',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Schema transformer for converting database room to select view
export const DbRoomToRoomSelect = Schema.transformOrFail(
  DbRoomWithBuildingSchema,
  RoomSelectSchema,
  {
    strict: true,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          return {
            value: raw.id,
            label: raw.number,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse room for select view',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Schema transformer for converting database room to edit view
export const DbRoomToRoomEdit = Schema.transformOrFail(
  DbRoomWithRelationsSchema,
  RoomEditSchema,
  {
    strict: true,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          // Transform categories to select format
          const categories = raw.categories?.map((assoc) => {
            const category = assoc.category;
            const categoryTranslations = category.translations || [];
            const categoryName =
              categoryTranslations.find((t) => t.locale === 'fr')?.name ||
              categoryTranslations.find((t) => t.locale === 'en')?.name ||
              categoryTranslations?.[0]?.name ||
              category.id;

            return {
              value: category.id,
              label: categoryName || category.id,
            };
          }) || [];

          return {
            id: raw.id,
            number: raw.number,
            area: raw.area,
            floorLoad: raw.floorLoad,
            buildingId: raw.buildingId,
            isActive: raw.isActive,
            categories,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse room for edit view',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Main serializer function that takes view parameter and returns appropriate transformation
export const dbRoomsToRooms = (
  dbRooms: Schema.Schema.Type<typeof DbRoomWithBuildingSchema>[],
  view: CollectionViewType,
): RoomList[] | RoomSelect[] => {
  return view === 'select'
    ? dbRooms.map((room) => Schema.decodeUnknownSync(DbRoomToRoomSelect)(room))
    : dbRooms.map((room) => Schema.decodeUnknownSync(DbRoomToRoomList)(room));
};

// New serializer function for Room with view parameter
export const dbRoomToRoom = (
  dbRoom: Schema.Schema.Type<typeof DbRoomWithRelationsSchema>,
  view: ResourceViewType,
) => {
  return view === 'edit'
    ? Schema.decodeUnknownSync(DbRoomToRoomEdit)(dbRoom)
    : Schema.decodeUnknownSync(DbRoomToRoomList)(dbRoom);
};

// Schema transformer for converting input format to database input format
export const RoomInputToDBInput = Schema.transformOrFail(
  RoomInputSchema,
  Schema.Struct({
    number: Schema.String,
    area: Schema.optional(Schema.Number),
    floorLoad: Schema.optional(Schema.Number),
    buildingId: Schema.optional(Schema.String),
    isActive: Schema.optional(Schema.Boolean),
  }),
  {
    strict: false,
    decode: (val, _options, ast) =>
      ParseResult.try({
        try: () => {
          return {
            number: val.number,
            area: val.area || undefined,
            floorLoad: val.floorLoad || undefined,
            buildingId: val.buildingId || undefined,
            isActive: val.isActive ?? true,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            val,
            error instanceof Error
              ? error.message
              : 'Failed to convert room input to DB input',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);
